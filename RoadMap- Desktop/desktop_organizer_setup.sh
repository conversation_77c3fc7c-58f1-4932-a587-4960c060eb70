#!/bin/bash
# Desktop Organizer - Complete Setup Script
# Voor Leon - Automatische desktop organisatie

set -e  # Stop bij eerste fout

echo "🚀 Desktop Organizer Setup voor Leon..."
echo "========================================="

# Variabelen
PROJECT_DIR="$HOME/.desktop-organizer"
DESKTOP_DIR="$HOME/Desktop"
SERVICE_NAME="com.leon.desktop-organizer"

# Maak project directory
echo "📁 Maken van project directory..."
mkdir -p "$PROJECT_DIR"
cd "$PROJECT_DIR"

# Check of Node.js geïnstalleerd is
if ! command -v node &> /dev/null; then
    echo "❌ Node.js niet gevonden!"
    echo "Installeer eerst Node.js: https://nodejs.org"
    exit 1
fi

echo "✅ Node.js gevonden: $(node --version)"

# Maak package.json
echo "📝 Maken van package.json..."
cat > package.json << 'EOF'
{
  "name": "desktop-organizer",
  "version": "1.0.0",
  "description": "Automatische desktop organisatie voor Leon",
  "main": "organizer.js",
  "scripts": {
    "start": "node organizer.js",
    "install-service": "node install-service.js"
  },
  "dependencies": {
    "chokidar": "^3.5.3"
  }
}
EOF

# Installeer dependencies
echo "📦 Installeren van dependencies..."
npm install

# Maak de hoofdscript
echo "🔧 Maken van organizer script..."
cat > organizer.js << 'EOF'
const fs = require('fs').promises;
const path = require('path');
const chokidar = require('chokidar');

class DesktopOrganizer {
    constructor() {
        this.desktop = path.join(require('os').homedir(), 'Desktop');
        this.logFile = path.join(__dirname, 'organizer.log');
        
        this.rules = {
            // Schermafbeeldingen per maand
            'schermafbeelding': () => `Screenshots/${new Date().toISOString().slice(0, 7)}`, // YYYY-MM
            
            // Documenten
            '.pdf': 'Documents/PDFs',
            '.doc': 'Documents/Word',
            '.docx': 'Documents/Word',
            '.txt': 'Documents/Text',
            
            // Media
            '.png': 'Images/PNG',
            '.jpg': 'Images/JPG',
            '.jpeg': 'Images/JPG',
            '.gif': 'Images/GIF',
            '.mp4': 'Videos',
            '.mov': 'Videos',
            '.avi': 'Videos',
            
            // Development
            '.js': 'Code/JavaScript',
            '.ts': 'Code/TypeScript',
            '.py': 'Code/Python',
            '.cpp': 'Code/CPP',
            '.json': 'Code/Config',
            
            // Archives
            '.zip': 'Archives',
            '.rar': 'Archives',
            '.tar': 'Archives',
            '.gz': 'Archives',
            
            // Design
            'figma': 'Design/Figma',
            'sketch': 'Design/Sketch'
        };
    }

    async log(message) {
        const timestamp = new Date().toISOString();
        const logEntry = `[${timestamp}] ${message}\n`;
        
        try {
            await fs.appendFile(this.logFile, logEntry);
            console.log(message);
        } catch (error) {
            console.error('Log error:', error);
        }
    }

    async createFolder(folderPath) {
        try {
            await fs.mkdir(folderPath, { recursive: true });
            return true;
        } catch (error) {
            await this.log(`Error creating folder ${folderPath}: ${error.message}`);
            return false;
        }
    }

    getTargetFolder(fileName) {
        const lowerName = fileName.toLowerCase();
        const ext = path.extname(lowerName);
        
        // Check voor specifieke patronen eerst
        for (const [pattern, folder] of Object.entries(this.rules)) {
            if (pattern.startsWith('.')) {
                // Extensie check
                if (ext === pattern) {
                    return typeof folder === 'function' ? folder() : folder;
                }
            } else {
                // Naam patroon check
                if (lowerName.includes(pattern)) {
                    return typeof folder === 'function' ? folder() : folder;
                }
            }
        }
        
        return 'Misc';
    }

    async organizeFile(filePath) {
        try {
            const fileName = path.basename(filePath);
            const targetFolderName = this.getTargetFolder(fileName);
            const targetFolder = path.join(this.desktop, targetFolderName);
            const targetPath = path.join(targetFolder, fileName);
            
            // Maak target folder als het niet bestaat
            await this.createFolder(targetFolder);
            
            // Check of target file al bestaat
            let finalPath = targetPath;
            let counter = 1;
            
            while (true) {
                try {
                    await fs.access(finalPath);
                    // File bestaat, maak nieuwe naam
                    const ext = path.extname(fileName);
                    const nameWithoutExt = path.basename(fileName, ext);
                    finalPath = path.join(targetFolder, `${nameWithoutExt}_${counter}${ext}`);
                    counter++;
                } catch {
                    // File bestaat niet, we kunnen deze naam gebruiken
                    break;
                }
            }
            
            // Verplaats het bestand
            await fs.rename(filePath, finalPath);
            await this.log(`✅ Moved: ${fileName} → ${targetFolderName}/`);
            
        } catch (error) {
            await this.log(`❌ Error organizing ${filePath}: ${error.message}`);
        }
    }

    async start() {
        await this.log('🚀 Desktop Organizer gestart...');
        
        // Organiseer bestaande bestanden eerst
        await this.organizeExistingFiles();
        
        // Start file watcher
        const watcher = chokidar.watch(this.desktop, {
            ignored: [
                /node_modules/,
                /\.git/,
                new RegExp(`^${this.desktop}/(Screenshots|Documents|Images|Videos|Code|Archives|Design|Misc)/`)
            ],
            persistent: true,
            ignoreInitial: true
        });
        
        watcher.on('add', (filePath) => {
            this.organizeFile(filePath);
        });
        
        watcher.on('error', async (error) => {
            await this.log(`❌ Watcher error: ${error}`);
        });
        
        await this.log('👀 Watching desktop for new files...');
        
        // Graceful shutdown
        process.on('SIGINT', async () => {
            await this.log('🛑 Stopping Desktop Organizer...');
            await watcher.close();
            process.exit(0);
        });
    }
    
    async organizeExistingFiles() {
        try {
            const files = await fs.readdir(this.desktop);
            const filesToOrganize = [];
            
            for (const file of files) {
                const filePath = path.join(this.desktop, file);
                const stats = await fs.stat(filePath);
                
                if (stats.isFile() && !file.startsWith('.')) {
                    filesToOrganize.push(filePath);
                }
            }
            
            await this.log(`📋 Found ${filesToOrganize.length} files to organize...`);
            
            for (const filePath of filesToOrganize) {
                await this.organizeFile(filePath);
            }
            
        } catch (error) {
            await this.log(`❌ Error organizing existing files: ${error.message}`);
        }
    }
}

// Start de organizer
const organizer = new DesktopOrganizer();
organizer.start().catch(console.error);
EOF

# Maak service installer
echo "⚙️ Maken van service installer..."
cat > install-service.js << 'EOF'
const fs = require('fs').promises;
const path = require('path');
const { spawn } = require('child_process');

const SERVICE_NAME = 'com.leon.desktop-organizer';
const PLIST_PATH = path.join(require('os').homedir(), 'Library', 'LaunchAgents', `${SERVICE_NAME}.plist`);
const PROJECT_DIR = __dirname;

async function createPlist() {
    const plistContent = `<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>Label</key>
    <string>${SERVICE_NAME}</string>
    <key>ProgramArguments</key>
    <array>
        <string>/usr/local/bin/node</string>
        <string>${PROJECT_DIR}/organizer.js</string>
    </array>
    <key>RunAtLoad</key>
    <true/>
    <key>KeepAlive</key>
    <true/>
    <key>StandardOutPath</key>
    <string>${PROJECT_DIR}/output.log</string>
    <key>StandardErrorPath</key>
    <string>${PROJECT_DIR}/error.log</string>
    <key>WorkingDirectory</key>
    <string>${PROJECT_DIR}</string>
</dict>
</plist>`;

    await fs.writeFile(PLIST_PATH, plistContent);
    console.log('✅ Service plist created');
}

async function installService() {
    try {
        await createPlist();
        
        // Load de service
        spawn('launchctl', ['load', PLIST_PATH], { stdio: 'inherit' });
        
        console.log('🎉 Service geïnstalleerd en gestart!');
        console.log(`📋 Service naam: ${SERVICE_NAME}`);
        console.log(`📁 Logs: ${PROJECT_DIR}/organizer.log`);
        console.log('');
        console.log('Commands:');
        console.log(`  Stop:  launchctl unload ${PLIST_PATH}`);
        console.log(`  Start: launchctl load ${PLIST_PATH}`);
        console.log(`  Logs:  tail -f ${PROJECT_DIR}/organizer.log`);
        
    } catch (error) {
        console.error('❌ Error installing service:', error);
    }
}

installService();
EOF

# Maak handige scripts
echo "🛠️ Maken van utility scripts..."

# Start script
cat > start.sh << 'EOF'
#!/bin/bash
cd "$(dirname "$0")"
echo "🚀 Starting Desktop Organizer..."
npm start
EOF

# Stop script  
cat > stop.sh << 'EOF'
#!/bin/bash
SERVICE_NAME="com.leon.desktop-organizer"
PLIST_PATH="$HOME/Library/LaunchAgents/${SERVICE_NAME}.plist"

echo "🛑 Stopping Desktop Organizer service..."
launchctl unload "$PLIST_PATH" 2>/dev/null || echo "Service was not running"
echo "✅ Service stopped"
EOF

# Status script
cat > status.sh << 'EOF'
#!/bin/bash
SERVICE_NAME="com.leon.desktop-organizer"
PROJECT_DIR="$HOME/.desktop-organizer"

echo "📊 Desktop Organizer Status"
echo "=========================="

# Check if service is running
if launchctl list | grep -q "$SERVICE_NAME"; then
    echo "✅ Service: RUNNING"
else
    echo "❌ Service: STOPPED"
fi

# Show recent logs
if [ -f "$PROJECT_DIR/organizer.log" ]; then
    echo ""
    echo "📋 Recent activity:"
    tail -5 "$PROJECT_DIR/organizer.log"
else
    echo "📋 No logs found"
fi
EOF

# Maak scripts executable
chmod +x start.sh stop.sh status.sh

echo ""
echo "🎉 Setup voltooid!"
echo "=================="
echo ""
echo "📁 Geïnstalleerd in: $PROJECT_DIR"
echo ""
echo "🚀 Om te starten:"
echo "   cd $PROJECT_DIR"
echo "   npm run install-service    # Installeer als background service"
echo "   # OF"
echo "   ./start.sh                 # Start handmatig"
echo ""
echo "📋 Beheer commands:"
echo "   ./status.sh                # Bekijk status"
echo "   ./stop.sh                  # Stop service"
echo "   tail -f organizer.log      # Bekijk live logs"
echo ""
echo "🗂️ Je bestanden worden georganiseerd in:"
echo "   Screenshots/YYYY-MM/       # Schermafbeeldingen per maand"
echo "   Documents/                 # PDF, Word, etc."
echo "   Images/                    # PNG, JPG, etc."
echo "   Code/                      # JS, Python, etc."
echo "   Archives/                  # ZIP, RAR, etc."
echo "   Design/                    # Figma, Sketch"
echo "   Misc/                      # Alles wat niet past"
echo ""
echo "✨ Klaar! Ga naar $PROJECT_DIR en run de install-service command!"